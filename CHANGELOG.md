CHANGELOG
-------------------------------

# 0.10.0
<PERSON> - Add weightshift capability to the remaining critical (tangent and sports) backed VCL services (PENG-29489)

# 0.9.0
<PERSON> - Update Fastly Terraform Priority (PENG-29237)

# 0.8.0
<PERSON> - PENG-28681 Enable call stacks in Reporting API crash reports (VCL header)

# 0.7.0
gsfondouris - ORD-8488 set cache-control = no-store for eu redirects

# 0.6.0
gsfondouris - ORD-8228 Synthetic cobrand footer for all browsers

# 0.5.0
gsfondouris - ORD-8101 Strip client IP from going to origin backends unless opt-in flag is present

# 0.4.0
<PERSON> - Ensure the SigSci blocking codes are not cached (406|429) (PENG-28738)

# 0.3.0
Bilal Fazal - static assets are missing the cache-control header as its not satisfying the default condition (assets-cache-control-fix)

# 0.2.0
<PERSON> - Setup NGWAF for meter-vcl (PENG-28608)

# 0.1.16
<PERSON> - ORD-7946 404 synthetic error routing rules.

# 0.1.15
gsfondouris - ORD-8306 Block suspicious requests from Singapore

# 0.1.14
gsfondouris - ORD-7942 migrate shared_tangent_set_cookie_gnt_i to shared_helpers_frontend_set_cookie_gnt_i + support entropy QSP override

# 0.1.13
George Sfondouris - ORD-8278 refactor TTL and SK for 3xx/4xx/5xx responses

# 0.1.12
gsfondouris - ORD-8267 Updates to Taboola user ID cookie (gnt_tb -> gnt_ti)

# 0.1.11
Carl Eichhorn - removed temporary moviemeter prod backend

# 0.1.10
Matt Rohland - Drop redundant surrogate keys for non-200 status.

# 0.1.9
Matt Rohland - Add unique TTL for 3XX, 4XX, and 5XX HTTP statuses.

# 0.1.8
George Sfondouris - ORD-8218 remove unconditional unset of beresp.http.cache-control and fix default ttl rule

# 0.1.7
Carl Eichhorn - Changed TLS cert hostname (PENG-28424-TLS)

# 0.1.6
Carl Eichhorn - commented url redirect for 2025 and switched to new runners (PENG-28509)

# 0.1.5
Carl Eichhorn - added additional wp-login logic (PENG-28424-W)

# 0.1.4
George Sfondouris - ORD-8200 admeter legacy redirect

# 0.1.3
Carl Eichhorn - Added admeter terms redirect (PENG-28495)

# 0.1.2
Carl Eichhorn - added wp-login logic (PENG-28424-WP)

# 0.1.1
Carl Eichhorn - Added temporary moviemeter prod backend

# 0.1.0
Bilal Fazal - convert sitecode to uppercase (sitecode-fix)

# 0.0.9
George Sfondouris - ORD-8100 redirect eu users

# 0.0.8
George Sfondouris - ORD-8058 add tangent integration

# 0.0.7
Matt Rohland - Add etag support and fix caching strategy for Ad Meter and Movie Meter.

# 0.0.6
Carl Eichhorn - Added tests(PENG-28363-A)

# 0.0.5
Carl Eichhorn - Added Moviemeter (PENG-28363)

# 0.0.4
Carl Eichhorn - Added caching (PENG-28296-B)

# 0.0.3
Carl Eichhorn - Added gcdnn (PENG-28296-B)

# 0.0.3
Carl Eichhorn - switched to shared login (PENG-28296-B)

# 0.0.2
Carl Eichhorn - Added admeter app (PENG-28296)

# 0.0.1
Carl Eichhorn - initial code implementation (PENG-28333)

