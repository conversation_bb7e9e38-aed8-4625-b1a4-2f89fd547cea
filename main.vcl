/*
This is the fastly boilerplate for custom vcl that also includes the vcl for serving stale content
Note: the lines that star with #FASTLY must stay in. These are where fastly injects additional configuration
see: https://docs.fastly.com/guides/vcl/mixing-and-matching-fastly-vcl-with-custom-vcl
*/

/*
guplib.vcl
*/
include "guplib";
/*
helpers.vcl includes debug header builders, host header adjustments, and log select functions.
*/
include "helpers";
/*
tables.vcl includes all the tables
*/
include "tables";
/*
tangent.vcl includes functionality specific to the tangent backend
*/
include "tangent";
/*
redirect.vcl include redirect logic
*/
include "redirects";
/*
traffic-mitigation.vcl includes functionality to block certain traffic
*/
# include "traffic-mitigation";
# shared snippets
include "snippet::shared_critical";
include "snippet::shared_helpers_general";
include "snippet::shared_helpers_frontend";
include "snippet::shared_proxy_gannett";
include "snippet::shared_proxy_vendor";
include "snippet::shared_eu";
include "snippet::shared_tangent";
include "snippet::shared_prebid";
include "snippet::shared_detection";

sub vcl_recv {
  set req.http.Gannett-custom:service-name = "Meter";

  if (req.url.qs ~ "gnt-debug") {
    set req.http.Gannett-Debug = "1";
  }

  # gannett boilerplate
  # logic to implement our own FF headers
  if (req.http.Gannett-FF-HMAC != digest.hmac_sha256(req.service_id + "HMACgannettHMAC", req.http.Gannett-FF)) {
    unset req.http.Gannett-FF-HMAC;
    unset req.http.Gannett-FF;
  }

  if (req.http.Fastly-Orig-Accept-Encoding) {
    if (req.http.Fastly-Orig-Accept-Encoding ~ "br") {
      set req.http.Accept-Encoding = "br";
    } elsif (req.http.Fastly-Orig-Accept-Encoding ~ "gzip") {
      set req.http.Accept-Encoding = "gzip";
    } else {
      unset req.http.Accept-Encoding;
    }
  }

  # Do only once per request lifetime (the first time through on an edge node before any restarts - won't run on the shield node)
  if (fastly.ff.visits_this_service == 0 && req.restarts == 0) {
    # lower environment ci, staging, origin-staging will need authrization header or office network to access
    call shared_helpers_general_low_env_auth;

    # set helper headers
    call set_site_details;
    set req.http.Gannett-Custom:Original-URL = req.url.path;
    set req.http.X-Forwarded-Host = req.http.host;

    # for testing weather / gnt_i data - leaving override in vcl_deliver alone
    if (req.http.gannett-geo-ip-override) {
      set client.geo.ip_override = req.http.gannett-geo-ip-override;
    }

    if (!req.http.Gannett-FF || (req.restarts > 0 && req.http.Gannett-FF == server.identity)) {
      set req.http.X-Forwarded-For = client.ip;
      set req.http.X-Forwarded-Host = req.http.host;
      if (req.http.Fastly-SSL) {
        set req.http.X-Forwarded-Proto = "https";
      } else {
        set req.http.X-Forwarded-Proto = "http";
      }
    }
    # strip incoming headers from request that are intended to only be set internally
    call shared_tangent_clean_incoming_request_headers;
    # Make sure the request coming into Fastly doesn't have these set, which can change behavior but should never be set outside of Fastly
    unset req.http.Fastly-Restart-On-Error;
    unset req.http.Fastly-Force-Shield;
    unset req.http.Fastly-No-Shield;

    ################### MITIGATION ###############

    # call mitigate_traffic;

    ################### REDIRECTS ################

    call shared_helpers_general_force_ssl_redirect;

    // avoid apex redirects for wordpress login on legacy platform
    if (req.url.path !~ "^/(wp-admin|wp-login|.well-known/acme-challenge)") {
      call shared_helpers_general_redirect_apex_domains_to_www;
    }

    call process_redirects_recv;

    ################### URL VALIDATION ###########

    call shared_helpers_frontend_strip_emails_from_qs;

    ################### DETECTION ################

    call shared_detection_detect_device;
    call shared_detection_detect_browser;
    call shared_detection_detect_os;

    ################### 4xx RULES ################

    call shared_helpers_frontend_status_404_rules;

    call shared_helpers_frontend_status_403_rules;

    ################ ROUTING ##################

    # determine if request should be a synthetic response
    call shared_helpers_front_end_process_synthetic_rules_recv;

    #### proxy to other VCL services
    # shared_prebid_recv - req.url.path ~ "^/pbd/" - return(pass) - proxy to prebid service backend
    call shared_prebid_recv;

    # shared_proxy_gannett_vcl_recv_gcias - req.url.path ~ "^/gciaf" - return(pass) - setup backend
    call shared_proxy_gannett_vcl_recv_gcias;

    # shared_proxy_gannett_vcl_recv_gcdn - req.url.path ~ "^/gcdn/" - setup x-origin-expect-host
    call shared_proxy_gannett_vcl_recv_gcdn;

    # shared_proxy_vendor_recv - return(pass) - sets auth and vendor reserve proxy backend for routing conditions defined below
    call shared_proxy_vendor_recv;

    # compute@edge

    #TODO sre_shared_compute_at_edge subroutines are dependent on implementation of certain subtourines in each service e.g. low_env_auth making it hard to use
    # instead we should call shared_lib version of subroutine e.g. shared_helpers_general_low_env_auth early in the service such that compute_at_edge code doesn't need to call the subroutine making the shared_lib portable

    if ( req.url.path ~ "^/eUdPVnUv" ) { # decode url encoded with base64
      declare local var.base64_path STRING;
      set var.base64_path = regsub(req.url, "^/eUdPVnUv", "");
      set req.url = "/" digest.base64url_decode(var.base64_path);
    }
    call shared_critical_vcl_recv_rec_ai;

    # Create/save any GUP IDs in req.http.GUP-Identifiers
    call guplib_recv_getset_ids;

    ##### backend routing #####

    # sitemaps
    if (
      req.url.path == "/web-sitemap-index.xml" ||
      req.url.path == "/news-sitemap.xml" ||
      req.url.path == "/video-sitemap-index.xml"
    ){
      set req.http.Gannett-Debug-Path-Item = "sitemaps";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "sitemaps";
      set req.http.x-origin-expected-host = "content_syndication_sitemap_production.storage.googleapis.com";
      declare local var.sitemap_replace_url STRING;
      set var.sitemap_replace_url = "/" req.http.Gannett-Custom:site-code "/";
      set req.url = regsub(req.url, "^/", var.sitemap_replace_url);
      set req.http.Date = now;
      set req.http.Authorization = "AWS GOOGYTGVTYJGDYZCKK46:" digest.hmac_sha1_base64("iGfd4UYsviXG9AnAHMTeXRp/EQXHda8LptJbtEz7", if(req.request == "HEAD", "GET", req.request) LF LF LF req.http.Date LF "/content_syndication_sitemap_production" req.url.path);
      set req.http.Gannett-Debug-Path-Item = req.url " sitemap url";
      call shared_helpers_general_record_object_path;
      set req.backend = F_storage_googleapis_com;
    }

    # DCJS / DCC
    if (req.url.path ~ "^/(dcjs|dcc)") {
      declare local var.t3-resource STRING;
      declare local var.surrogate-env STRING;
      set var.t3-resource = re.group.1;
      set req.http.Gannett-Debug-Path-Item = var.t3-resource " -> gannett-cdn" " " time.elapsed.msec "ms";
      call shared_helpers_general_record_object_path;
      set var.surrogate-env = regsub(req.url, "(/dcjs|/dcc)", "");
      if ( var.surrogate-env ~ "^/(dev|prod|stage)") {
        set req.http.x-append-surrogate = table.lookup(gannett_cdn_proxy,var.t3-resource) "-env-" re.group.1;
      }
      if (req.url.path ~ "^/dcjs/.*q1a2z3.*") {
        set req.http.x-append-surrogate = "data-collection-javascript-static";
      }
      set req.http.x-origin-expected-host = "www.gannett-cdn.com";
      set req.http.Gannett-Custom:disable-vary-cache = "true";
    }

    # TEAL
    if (req.url.path ~ "^/gannett-web/apps/teal/dist/") {
      set req.http.Gannett-Debug-Path-Item =  "videoscript -> gannett-cdn" " " time.elapsed.msec "ms";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "videoscript";
      set req.http.x-origin-expected-host = "www.gannett-cdn.com";
      if (req.http.Accept-Encoding ~ "br" && req.url.ext == "js") {
        set req.url = regsub(req.url, "\.min\.js",".min.js.br");
      }
    }

    # legacy admeter (do we still need?)
    if (
      req.http.Gannett-Custom:site-name == "admeter" &&
      req.url.path ~ "^/results/"
    ) {
      set req.http.x-origin-expected-host = "usatoday.go-vip.net";
      set req.http.Gannett-Debug-Path-Item = "goVip legacy origin";
      call shared_helpers_general_record_object_path;
      set req.http.x-append-surrogate = "govip";
    }

    ### tangent url validation

    # correct or redirect to /errors/404/
    call shared_tangent_redirect_malformed_urls;
    # only trailing slash version of pages without extensions allowed
    if (req.http.x-origin-expected-host ~ "^tangent") {
      call shared_tangent_redirect_on_missing_trailing_slash;
    }
    # tangent
    if (!req.http.x-origin-expected-host) {
      call process_tangent_rules;
    }

  # END do only once per request lifetime
  } elseif (req.restarts > 0) {
    if (req.url.path ~ "^/errors/") {
      call shared_tangent_error_pages;
      # re-enable clustering after a error page restart
      set req.http.Fastly-Force-Shield = "1";

      set req.http.Gannett-Debug-Path-Item = "FFS enabled on restart: " req.restarts;
      call shared_helpers_general_record_object_path;
    }
  }

  if (req.http.Gannett-Debug) {
    if (!req.http.Fastly-FF) {
      set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " ; "  "vcl-recv-pop-r " + req.restarts " " time.elapsed.msec "ms";
    } elseif (req.http.Fastly-FF) {
      set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " ; "  "vcl-recv-shield-r " + req.restarts " " time.elapsed.msec "ms";
    }
  }

  ##### default rule
  if (!req.http.x-origin-expected-host) {
    call shared_helpers_frontend_supported_browser_check_gannett;
    set req.http.Gannett-Debug-Path-Item = "DX backend";
    call shared_helpers_general_record_object_path;

    set req.http.x-append-surrogate = req.http.Gannett-Custom:site-name; # e.g. admeter
    set req.http.Gannett-Custom:DX = "1";

    # br encoding is not supported, if client supports gzip override original decision
    if (req.http.Fastly-Orig-Accept-Encoding ~ "gzip") {
      set req.http.Accept-Encoding = "gzip";
    }
  }

  ##### bare minumum browser version checks for qualified pages
  if (
    req.http.Gannett-Custom:tng-supported  != "1" &&
    req.http.x-origin-expected-host !~ "^tangent" &&
    req.url.path != "/services/cobrand/"
  ) {
    call shared_helpers_frontend_supported_browser_check_gannett;
  }

  ##  Legacy wordpress fetch ##
  if (
      req.http.Cookie ~ "(wordpress_|PHPSESSID)" ||
      req.url.path ~ "^/_static/" ||
      req.url.path ~ "^/.well-known/acme-challenge/" ||
      req.url.path ~ "^/wp-includes/" ||
      req.url.path ~ "^/wp-content/" ||
      req.url.path ~ "^/wp-admin/" ||
      req.url.path ~ "^/wp-activate.php" ||
      req.url.path ~ "^/wp-json/" ||
      req.url.path == "/wp-login.php" ||
      uuid.is_version4(req.url.path)
   ) {
      set req.http.x-origin-expected-host = "usatoday.go-vip.net";
      set req.http.Gannett-Custom:wp-legacy-fetch = "";
      set req.http.Gannett-Debug-Path-Item = "WP legacy origin recv";
      call shared_helpers_general_record_object_path;
  }

  # Apply actual backend selection
  if (req.http.x-origin-expected-host == "www.gannett-cdn.com"){
    set req.http.Gannett-Debug-Path-Item = req.http.x-origin-expected-host " backend";
    call shared_helpers_general_record_object_path;
    set req.http.x-append-surrogate = req.http.x-append-surrogate " " "gannett-cdn";
    set req.backend = F_www_gannett_cdn_com;
  } elseif (req.http.x-origin-expected-host ~ "storage.googleapis.com"){
    set req.http.Gannett-Debug-Path-Item = req.http.x-origin-expected-host " backend";
    call shared_helpers_general_record_object_path;
    set req.backend = F_storage_googleapis_com;
  } elseif (req.http.x-origin-expected-host == "usatoday.go-vip.net") { # legacy wordpress origin
    set req.http.Gannett-Debug-Path-Item = req.http.x-origin-expected-host "WP backend";
    call shared_helpers_general_record_object_path;
    set req.backend = F_usatoday_go_vip_net;
    # WP admin pages should not be cached
    if (req.http.Cookie ~ "(wordpress_|PHPSESSID)" || req.url.path ~ "^/(wp-admin|wp-login)" || req.url.path ~ "^/.well-known/acme-challenge/") {
      return(pass);
    }
  } elseif (req.http.x-origin-expected-host ~ "^tangent"){
    if (randombool(std.atoi(table.lookup(weight_shift, "tangent", "50")), 100)) {
      if (req.http.x-origin-expected-host ~ "^tangent-fragments") {
        set req.http.Gannett-Debug-Path-Item = "region: tangent-fragments_" req.http.Gannett-Custom:site-name " east";
        set req.backend = F_tangent_fragments_east;
      } else {
        set req.http.Gannett-Debug-Path-Item = "region: tangent_" req.http.Gannett-Custom:site-name " east";
        set req.backend = F_tangent_east;
      }
      call shared_helpers_general_record_object_path;
    } else {
      if (req.http.x-origin-expected-host ~ "^tangent-fragments") {
        set req.http.Gannett-Debug-Path-Item = "region: tangent-fragments_" req.http.Gannett-Custom:site-name " west";
        set req.backend = F_tangent_fragments_west;
      } else {
        set req.http.Gannett-Debug-Path-Item = "region: tangent_" req.http.Gannett-Custom:site-name " west";
        set req.backend = F_tangent_west;
      }
      call shared_helpers_general_record_object_path;
    }
    # check that the backend is healthy
    if (!req.backend.healthy) {
      if(req.backend == F_tangent_east) {
        set req.backend = F_tangent_west;
        set req.http.Gannett-Debug-Path-Item = "tangent_" req.http.Gannett-Custom:site-name " east unhealthy";
        
      } elseif (req.backend == F_tangent_west && !req.backend.healthy) {
        set req.backend = F_tangent_east;
        set req.http.Gannett-Debug-Path-Item = "tangent_" req.http.Gannett-Custom:site-name " west unhealthy";
        call shared_helpers_general_record_object_path;
      } elseif (req.backend == F_tangent_fragments_east && !req.backend.healthy) {
        set req.backend = F_tangent_fragments_west;
        set req.http.Gannett-Debug-Path-Item = "tangent-fragments_" req.http.Gannett-Custom:site-name " east unhealthy";

      } elseif (req.backend == F_tangent_fragments_west && !req.backend.healthy) {
        set req.backend = F_tangent_fragments_east;
        set req.http.Gannett-Debug-Path-Item = "tangent-fragments_" req.http.Gannett-Custom:site-name " west unhealthy";
      }
      call shared_helpers_general_record_object_path;
    }
  } elseif (req.http.Gannett-Custom:DX) {
    if (randombool(std.atoi(table.lookup(weight_shift, "meter", "100")), 100)) {
      set req.backend = F_meter_east;
    } else {
      set req.backend = F_meter_west;
    }

    # check that the backend is healthy
    if (!req.backend.healthy) {
        if (req.backend == F_meter_east) {
            set req.backend = F_meter_west;
            set req.http.Gannett-Debug-Path-Item = "east unhealthy";
        } else {
            set req.backend = F_meter_east;
            set req.http.Gannett-Debug-Path-Item = "west unhealthy";
        }
        call shared_helpers_general_record_object_path;
    }

    # setup host name
      if(req.http.host ~ "^origin-staging-") {
        set req.http.x-origin-expected-host = "campaign-games-us-east1-45898334026.us-east1.run.app";
      } else {
        if(req.backend == F_meter_east) {
          set req.http.x-origin-expected-host = "campaign-games-us-east1-664912083968.us-east1.run.app";
        } else {
          set req.http.x-origin-expected-host = "campaign-games-us-west1-664912083968.us-west1.run.app";
        }
      }
  }

  # This code executes on edge which is always hit as opposed to the shield. Any code that needs to run per user/hit
  # should run on edge instead of shield.
  if (!req.http.Gannett-FF || (req.restarts > 0 && req.http.Gannett-FF == server.identity)) {
    ############### CAN RUN COOKIES
    call shared_helpers_tangent_can_run_cookie_logic_recv;
    # Only keep around cookies that we know we need.
    call recv_sanitize_cookie_header;
    if (req.http.Gannett-Debug) {
        set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " ; "  "recv_sanitize_cookie_header" " " time.elapsed.msec "ms";
    }
    # check for entropy qsp override
    call shared_helpers_frontend_update_gnt_i_entropy;
  }

  ############## APPEND SURROGATES
  if (req.url.path ~ "/(\d{4})/(\d{2})/(\d{2})/") {
    if(!req.http.x-append-surrogate){
      # set full date (2017-01-01) as surrogate key
      set req.http.x-append-surrogate = re.group.1 "-" re.group.2 "-" re.group.3;
      # set year-month as surrogate key
      set req.http.x-append-surrogate = req.http.x-append-surrogate " " re.group.1 "-" re.group.2;
    } else {
      # set full date (2017-01-01) as surrogate key
      set req.http.x-append-surrogate = req.http.x-append-surrogate " " re.group.1 "-" re.group.2 "-" re.group.3;
      # set year-month as surrogate key
      set req.http.x-append-surrogate = req.http.x-append-surrogate " " re.group.1 "-" re.group.2;
    }
  }

  ### for legacy platform
  if (req.http.x-origin-expected-host == "usatoday.go-vip.net") {
    set req.http.x-append-surrogate = if (req.http.x-append-surrogate, req.http.x-append-surrogate " ", "") " go-vip legacy " req.http.Gannett-Custom:site-name;
  } else {
    ### for tangent platform
    if (req.url.path ~ "^/story/") {
      set req.http.x-append-surrogate = req.http.x-append-surrogate " asset_story asset_story_" req.http.Gannett-Custom:site-name ;
    }

    if (req.url.path ~ "^/videos/") {
      set req.http.x-append-surrogate = req.http.x-append-surrogate " asset_video asset_video_" req.http.Gannett-Custom:site-name;
    }

    if (req.url.path ~ "^/picture-gallery/") {
      set req.http.x-append-surrogate = req.http.x-append-surrogate " asset_gallery asset_gallery_" req.http.Gannett-Custom:site-name;
    }

    if (req.url.path ~ "^/media/") {
      set req.http.x-append-surrogate = req.http.x-append-surrogate " media media_" req.http.Gannett-Custom:site-name;
    }

    if (req.url.path ~ "^/search/") {
      set req.http.x-append-surrogate = req.http.x-append-surrogate " search search_" req.http.Gannett-Custom:site-name;
    }
  }

  if (req.http.Fastly-FF) {
    set req.max_stale_while_revalidate = 0s;
  }

#FASTLY recv
  if (req.request != "HEAD" && req.request != "GET" && req.request != "FASTLYPURGE") {
    return(pass);
  }

  # gannett boilerplate
  # logic to implement our own FF header
  if (fastly.ff.visits_this_service == 0) {
    set req.http.Gannett-FF = server.identity;
  } elsif (!std.suffixof(req.http.Gannett-FF, server.identity)) {
    set req.http.Gannett-FF = req.http.Gannett-FF + ", " + server.identity;
  }
  # secret key should match the one at the top
  set req.http.Gannett-FF-HMAC = digest.hmac_sha256(req.service_id + "HMACgannettHMAC", req.http.Gannett-FF);

  return(lookup);
}

sub vcl_hash {

  if (req.url.qs ~ "^utm_source=taboola" || req.url.qs ~ "&utm_source=taboola") {
    set req.hash += querystring.filter(req.url, "utm_source" + querystring.filtersep() + "utm_medium" querystring.filtersep() + "utm_campaign");
  } else {
    # convert gnt-mobile QSP to header to continue supporting it for manual testing on Tangent only and ensure its stripped for other platforms
    if (req.url.qs ~ "gnt-mobile") {
      if (req.http.x-origin-expected-host ~ "^tangent") {
        call shared_tangent_set_mobile;
      } else {
        set req.url = querystring.filter(req.url, "gnt-mobile");
      }
    }
    if (req.http.x-origin-expected-host ~ "^tangent") {
      # prepend additional query string params to default whitelist based on path
      if (req.url.path ~ "^/search") {
        set req.url = querystring.regfilter_except(req.url, "q|page|tangent|gnt-debug|gnt-unclean|gnt-basket");
        if (req.url.qs ~ "^page=1" || req.url.qs ~ "&page=1" || (req.url.qs !~ "^q=" && req.url.qs !~ "&q=")) {
          if (req.http.Gannett-Debug) {
            set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " ; "  "search strip_page" " " time.elapsed.msec "ms";
          }
          set req.url = querystring.regfilter(req.url, "page"); # limit cached entries for 1st page of search results since ?q=term&page=1 == ?q=term
        }
      } elseif (req.url.path ~ "^/tangsvc/pg/") {
        set req.url = querystring.regfilter_except(req.url, "ids|tangent|gnt-debug|gnt-unclean|gnt-basket");
      } elseif (req.url.path ~ "^/tangfrag/") {
        set req.url = querystring.regfilter_except(req.url, "tangent|gnt-debug|gnt-unclean|gnt-basket|prm-");
      } else {
        # default tangent qsp whitelist
        set req.url = querystring.regfilter_except(req.url, "tangent|gnt-debug|gnt-unclean|gnt-basket");
      }
    }
    set req.url = boltsort.sort(req.url);
    set req.hash += req.url;
  }

  if (req.http.Gannett-Custom:DX) {
    /*
      Adding cache support for etags.
      Varying the hash on these headers is necessary if you are passing the etag header through to the client.
    */
    if (req.http.If-None-Match) {
      set req.hash += "If-None-Match";
      set req.hash += req.http.If-None-Match;
    }
    if (req.http.If-Match) {
      set req.hash += "If-Match";
      set req.hash += req.http.If-Match;
    }
  }

  # don't create additional cache entries for identical resources across sites
  if (
    (req.http.x-origin-expected-host !~ "^tangent" || req.url.path !~ "^/tangstatic/") &&
    !req.http.Gannett-Custom:disable-vary-cache
  ) {
    set req.hash += req.http.host;
  }

  set req.hash += req.vcl.generation;
  return(hash);

#FASTLY hash
}

sub vcl_hit {
  #FASTLY hit

    unset obj.http.Gannett-Debug-Path-Full;
    unset obj.http.Gannett-Debug-Path;

    if (!obj.cacheable) {
      return(pass);
    }
    return(deliver);
}

sub vcl_miss {
  if (req.http.Gannett-Debug) {
    if (!req.http.Fastly-FF) {
      set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " ; "  "vcl-miss-pop-r" + req.restarts " " time.elapsed.msec "ms";
    } elseif (req.http.Fastly-FF) {
      set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " ; "  "vcl-miss-shield-r" + req.restarts " " time.elapsed.msec "ms";
    }
  }
  call set_hostheader_for_origin;
  if(req.http.Gannett-Debug){
    set bereq.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " --->";
  }
#FASTLY miss
  #Fastly suggested change. Check CHANGELOG 1.39.5
  if (bereq.http.X-Forwarded-Host ~ "^([^,]+),[^,]*.*$") {
    set bereq.http.X-Forwarded-Host = re.group.1;
  }

  if (req.http.X-Block-Request == "true") {
    error 903 "Forbidden";
  }

  # strip certain headers from going to origin
  unset req.http.x-original-url;
  unset req.http.x-rewrite-url;
  if (req.backend.is_origin) {
    call shared_helpers_general_strip_client_ip_from_bereq;
    unset bereq.http.vcl_data;
    unset bereq.http.Gannett-FF;
    unset bereq.http.Gannett-FF-HMAC;
    unset bereq.http.Fastly-Cachetype;
    unset bereq.http.Fastly-Debug-Digest;
    unset bereq.http.Fastly-Debug-Path;
    unset bereq.http.Fastly-Debug-TTL;
    unset bereq.http.Fastly-Debug;

    if (req.http.x-origin-expected-host ~ "^tangent") {
      call shared_tangent_clean_backend_request_headers;
    }
  }

  if (req.http.Gannett-Debug) {
    set req.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " ; bereq.headers #: " std.count(bereq.headers);
  }

  # shared_proxy_gannett_vcl_miss_gcdn - req.http.Gannett-Custom:gcdn - clean request headers
  call shared_proxy_gannett_vcl_miss_gcdn;

  return(fetch);
}

sub vcl_pass {
  if(req.http.Gannett-Debug){
    set bereq.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path " --->";
  }
  call set_hostheader_for_origin;
#FASTLY pass
  #Fastly suggested change. Check CHANGELOG 1.39.5
  if (bereq.http.X-Forwarded-Host ~ "^([^,]+),[^,]*.*$") {
    set bereq.http.X-Forwarded-Host = re.group.1;
  }

  # strip certain headers from going to origin
  unset req.http.x-original-url;
  unset req.http.x-rewrite-url;
  if (req.backend.is_origin) {
    call shared_helpers_general_strip_client_ip_from_bereq;
    unset bereq.http.vcl_data;
    unset bereq.http.Gannett-FF;
    unset bereq.http.Gannett-FF-HMAC;
    unset bereq.http.Fastly-Cachetype;
    unset bereq.http.Fastly-Debug-Digest;
    unset bereq.http.Fastly-Debug-Path;
    unset bereq.http.Fastly-Debug-TTL;
    unset bereq.http.Fastly-Debug;
  }
  # shared_proxy_gannett_vcl_pass_gcias - req.http.x-origin-expected-host ~ "gcias-compute.usatoday.com" - return(fetch) - set x-forwarded-for header
  call shared_proxy_gannett_vcl_pass_gcias;

  # shared_proxy_gannett_vcl_pass_gcdn - req.http.Gannett-Custom:gcdn - return(fetch) - clean request headers
  call shared_proxy_gannett_vcl_pass_gcdn;
}

sub vcl_fetch {
  if (req.backend.is_origin) {
    set beresp.http.backend_ip = beresp.backend.ip;
  }

  # prebid - don't override vcl_fetch logic from prebid service
  call shared_prebid_fetch;

  # shared_proxy_vendor_fetch - return(deliver) - early exit from vcl_fetch to avoid override of vcl_fetch logic from reverse-proxy-vcl service
  call shared_proxy_vendor_fetch;

  #rec-ai ttl set
  call shared_critical_vcl_fetch_rec_ai;

  # shared_proxy_gannett_vcl_fetch_gcias - req.http.x-origin-expected-host ~ "gcias-compute.usatoday.com" - return pass - setup ttl and cache-control
  call shared_proxy_gannett_vcl_fetch_gcias;

  # shared_proxy_gannett_vcl_fetch_gcdn - req.http.Gannett-Custom:gcdn - return(deliver)- setup ttl
  call shared_proxy_gannett_vcl_fetch_gcdn;

  ## surrogates
  if (!req.backend.is_shield) {
    set beresp.http.Surrogate-Key = if (beresp.http.Surrogate-Key, beresp.http.Surrogate-Key " ", "") req.url.dirname " " req.url.path " " beresp.status " " if(req.http.x-append-surrogate, req.http.x-append-surrogate, "");
    if (beresp.status >= 400 && beresp.status < 600) {
      set beresp.http.Surrogate-Key  = beresp.http.Surrogate-Key " cache_error";
    }
  }

  ### vary
  # preserve Vary headers before stripping and rebuilding with selected Vary headers
  declare local var.preserve_vary STRING;
  set var.preserve_vary = beresp.http.Vary;

  # strip vary from backend response before rebuilding with desired preserved Vary headers
  unset beresp.http.Vary;

  # It's safer to Vary on Cookie now that we're throwing out any unknown cookies
  if (var.preserve_vary ~ "Cookie") {
    set beresp.http.vary:Cookie = "";
  }
  # Accept-Encoding could get set at origin or at the shield (by Fastly gzip boilerplate)
  if (var.preserve_vary ~ "Accept-Encoding") {
    set beresp.http.vary:Accept-Encoding = "";
  }
  # Accept could get set at www.gannett-cdn.com origin
  if (req.http.Gannett-Custom:gcdn) {
    if (var.preserve_vary ~ "Accept") {
      set beresp.http.vary:Accept = "";
    }
  }

  # Set from Tangent origin for page requests that have desktop/mobile variants
  if (var.preserve_vary ~ "Gnt-Mobile") {
    set beresp.http.vary:Gnt-Mobile = "";
  }

  # # Set from Tangent origin for in/out market for sports betting
  # if (var.preserve_vary ~ "gnt-gm-im") {
  #   set beresp.http.vary:gnt-gm-im = "";
  # }

  # # Set from Tangent origin for banner alerts
  # if (var.preserve_vary ~ "gnt-ba") {
  #   set beresp.http.vary:gnt-ba = "";
  # }

  # need to vary on req.http.x-origin-expected-host since we never adjust req.http.host
  # and we have static rule that will hash to the same uri
  if (
    # don't create additional cache entries for identical Tangent resources across sites
    (req.http.x-origin-expected-host !~ "^tangent" || req.url.path !~ "^/tangstatic/") &&
    !req.http.Gannett-Custom:disable-vary-cache
  ) {
    set beresp.http.vary:x-origin-expected-host = "";
  }

  # protect from caching empty 200
  if (beresp.status == 200 && beresp.http.Content-Length == "0") {
    return(pass);
  }

  # gannett boilerplate

  # gzip compression
  if ((beresp.status == 200 || beresp.status == 404) && (beresp.http.content-type ~ "^(text\/html|application\/x\-javascript|text\/css|application\/javascript|text\/javascript|application\/json|application\/vnd\.ms\-fontobject|application\/x\-font\-opentype|application\/x\-font\-truetype|application\/x\-font\-ttf|application\/xml|font\/eot|font\/opentype|font\/otf|image\/svg\+xml|image\/vnd\.microsoft\.icon|text\/plain|text\/xml)\s*($|;)" || req.url ~ "\.(css|js|html|eot|ico|otf|ttf|json|svg)($|\?)" ) ) {
    # always set vary to make sure uncompressed versions dont always win
    if (!beresp.http.vary:accept-encoding) {
        set beresp.http.vary:accept-encoding = "";
    }
    # gzip setup as a default compression. If service needs brotli compression, the following block needs modification
    if (req.http.Accept-Encoding ~ "gzip") {
      set beresp.gzip = true;
    }
  }

  /* cache HTTP non-2XXs */
  if (beresp.status >= 300 && beresp.status < 600) {
    # Do not cache SigSci errors
    if(beresp.http.x-sigsci-agentresponse ~ "(406|429)") {
      return(deliver);
    }

    /* deliver stale if the object is available */
    if (stale.exists) {
      return(deliver_stale);
    }

    set beresp.cacheable = true;

    if (beresp.http.Surrogate-Control !~ "max-age") {
      if(req.http.x-origin-expected-host ~ "storage.googleapis.com"){
        set beresp.ttl = 5s;
      } else {
        if (beresp.status >= 500) {
          set beresp.ttl = 5s;
        } else {
          set beresp.ttl = 60s;
        }
      }
    }

    if (req.http.x-origin-expected-host ~ "^tangent-fragments" && !req.backend.is_shield) {
      # serve synthetic responses
      if (beresp.status >= 500) {
        error 500;
      } elseif (beresp.status >= 400) {
        # serve synthetic 404
        error 804;
      }
    }

    # construct our debug headers
    if (req.http.Gannett-Debug) {
      set beresp.http.Gannett-Debug-Path-Fetch = if(beresp.http.Gannett-Debug-Path-Fetch, beresp.http.Gannett-Debug-Path-Fetch " ttl: " beresp.ttl, " ttl: " beresp.ttl);
    }

    return(deliver);
  }

 #FASTLY fetch

  if ((beresp.status == 500 || beresp.status == 503) && req.restarts < 1 && (req.request == "GET" || req.request == "HEAD")) {
    restart;
  }

  if (req.restarts > 0) {
    set beresp.http.Fastly-Restarts = req.restarts;
  }

  unset beresp.http.Set-Cookie;
  if (beresp.http.Set-Cookie) {
    set req.http.Fastly-Cachetype = "SETCOOKIE";
    return(pass);
  }

  if (beresp.http.Cache-Control ~ "private" && !req.http.x-origin-expected-host ~ "storage.googleapis.com") {
    set req.http.Fastly-Cachetype = "PRIVATE";
    return(pass);
  }

  if (beresp.http.Cache-Control ~ "no-cache") {
    set req.http.Fastly-Cachetype = "NO-CACHE";
    return(pass);
  }

  if (beresp.http.Cache-Control ~ "no-store" && (!req.http.x-origin-expected-host ~ "cet-front-end-static")) { # no-store is set to prevent caching on GCS bucket, but we still want it cached in Fastly
    set req.http.Fastly-Cachetype = "NO-STORE";
    return(pass);
  }

  if(!req.http.x-append-surrogate){
    set req.http.x-append-surrogate = "";
  }

  #################### CACHE RULES ############################

  /* set stale_if_error and stale_while_revalidate (customize these values) */
  set beresp.stale_if_error = 86400s;
  set beresp.stale_while_revalidate = 20s;

  if (req.url.path ~ "/web-sitemap-index.xml" ||
    req.url.path ~ "/news-sitemap.xml" ||
    req.url.path ~ "/video-sitemap-index.xml") {
    set beresp.stale_while_revalidate = 0s;
  } elseif (req.http.Gannett-Custom:DX) {
    set beresp.stale_while_revalidate = 0s;
  } elseif (req.http.x-origin-expected-host ~ "^tangent") {
    set beresp.stale_while_revalidate = 5m;
  }


  # DX ttls
  if (req.http.gannett-custom:DX) {
    # apply the default ttl
    set beresp.ttl = 65m;
    if (req.url.path ~ "^/api/data") {
      set beresp.ttl = 60s;
    } elseif (std.prefixof(beresp.http.Content-Type, "text/html")) {
      set beresp.ttl = 120m;
    } elseif (req.url.path ~ "^/_next") {  # Specifically set browser cache rules for fonts
      set beresp.http.Cache-Control = "public, max-age=31536000, immutable";
      set beresp.ttl = 30d;
    # keep TTL that was provided by Tangent backend, otherwise apply TTL rules
    }
    call shared_tangent_keep_origin_cache_control;
  } elseif (req.http.x-origin-expected-host !~ "^tangent" || beresp.http.Surrogate-Control !~ "max-age") {
    # apply the default ttl
    set beresp.ttl = 240m;
    if (
      req.http.x-origin-expected-host != "usatoday.go-vip.net" &&
      (
        req.url.path ~ "/20[0123456789]{2}/[0123456789]{2}/" ||
        req.url.path ~ "^/.cam-tangent/asset/"
      )
    ) {
      set beresp.ttl = 30d;
    } else if (req.http.x-origin-expected-host == "content_syndication_sitemap_production.storage.googleapis.com") {
      set beresp.ttl = 3m;
    } else if (req.http.x-origin-expected-host == "www.gannett-cdn.com") { # these uris go to www.gannett-cdn.com, match the ttl rules that are there. This will eventually get cleaned up
      set beresp.ttl = 3m;
      if (req.url.path ~ "^/(dcjs|dcc)") {
        set beresp.ttl = 1h;
        set beresp.http.Cache-Control = "max-age=3600";
        if (req.url.path ~ "^/dcjs/.*q1a2z3.*") {
          set beresp.ttl = 10y;
          set beresp.http.Cache-Control = "public,immutable,max-age=315360000";
          set beresp.stale_while_revalidate = 20y;
          set beresp.stale_if_error = 20y;
        }
        call shared_tangent_keep_origin_cache_control;
      }
    }
  }

  # additional DX platform rules
  if (req.http.gannett-custom:DX) {
    # Set failover TTL in the case there is an SK overflow from CAPI. The presence of Surrogate-Control:max-agent means there was an overflow.
    if (beresp.http.Surrogate-Control ~ "max-age"){
      set beresp.http.Surrogate-Control = "max-age=15"; #override to 15 seconds
      set beresp.ttl = 15s;
    }

    # set security policy headers
    set beresp.http.Content-Security-Policy = "upgrade-insecure-requests;frame-ancestors 'none';object-src 'none'";
    set beresp.http.Content-Security-Policy-Report-Only = "script-src https: blob: 'unsafe-inline' 'unsafe-eval' 'self';base-uri 'self';report-uri https://reporting-api.gannettinnovation.com";
    set beresp.http.Feature-Policy = "camera 'none';display-capture 'none';geolocation 'none';microphone 'none';payment 'none';usb 'none';xr-spatial-tracking 'none'";
    set beresp.http.Permissions-Policy = "bluetooth=(),camera=(),display-capture=(),geolocation=(),hid=(),identity-credentials-get=(),local-fonts=(),microphone=(),midi=(),otp-credentials=(),payment=(),publickey-credentials-create=(),publickey-credentials-get=(),serial=(),usb=(),window-management=(),xr-spatial-tracking=()";
    set beresp.http.Referrer-Policy = "strict-origin-when-cross-origin";
    set beresp.http.X-Content-Type-Options = "nosniff";
    set beresp.http.X-Frame-Options = "deny";
    set beresp.http.X-XSS-Protection = "1; mode=block";
    set beresp.http.Cross-Origin-Resource-Policy = "same-origin";

    # Dont PASS the HTML page because we are caching it on Fastly
    if( !(std.prefixof(beresp.http.Content-Type, "text/html")) && req.url.path !~ "^/api/data" ) {
      if (beresp.http.Cache-Control ~ "(private|no-store|no-cache)") {
        return(pass);
      }
    }
  }

  # 301s with non cache-control are dangerous, as chrome and firefox will cache them forever
  # if the backend gave us a cache-control, let that pass, otherwise set one
  if ( beresp.status == 301 ) {
    if ( ! beresp.http.Cache-Control ) {
      set beresp.http.Cache-Control = "max-age=30";
    }
  } elseif (!req.http.Gannett-Custom:keep-origin-cache-control) {
    if (req.url.path ~ "/tangsvc") {
      if (beresp.http.Surrogate-Control !~ "max-age") {
        set beresp.ttl = 7d;
      }
      set beresp.http.Cache-Control = "public, max-age=60";
    } elseif (req.url.path ~ "^/gannett-web/apps/teal/dist/") {
      set beresp.ttl = 30d;
      set beresp.http.Cache-Control = "public, immutable, max-age=315360000";
    } else {
      # removing cache-control header for instant publish PAAS-5224
      unset beresp.http.Cache-Control;
    }
  }

  unset beresp.http.Expires;

  if (req.http.Gannett-Debug) {
    set beresp.http.Gannett-Debug-Path-Fetch = if(beresp.http.Gannett-Debug-Path-Fetch, beresp.http.Gannett-Debug-Path-Fetch " ttl: " beresp.ttl, " ttl: " beresp.ttl);
  }

  return(deliver);
}

sub vcl_deliver {

  if (req.http.Gannett-Debug) {
    if (resp.http.Gannett-Debug-Path-Fetch) {
      set resp.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path "; " resp.http.Gannett-Debug-Path-Fetch;
    } else {
      set resp.http.Gannett-Debug-Path = req.http.Gannett-Debug-Path;
    }
    set resp.http.Gannett-Debug-Path-Full = if(resp.http.Gannett-Debug-Path-Full, " restarts: " req.restarts " shield: " if(req.backend.is_shield, "true", "false") " server: " server.identity " state: " fastly_info.state " path: " resp.http.Gannett-Debug-Path " >>>> " resp.http.Gannett-Debug-Path-Full, " restarts: " req.restarts " shield: " if(req.backend.is_shield, "true", "false") " server: " server.identity " state: " fastly_info.state " path: " resp.http.Gannett-Debug-Path " Service: www.usatoday.com");
  }
  unset resp.http.Gannett-Debug-Path-Fetch;

  # prebid - don't override vcl_deliver logic from prebid service
  call shared_prebid_deliver;

  # shared_proxy_vendor_deliver - return(deliver) - early exit from vcl_deliver to avoid override of vcl_deliver logic from reverse-proxy-vcl service
  call shared_proxy_vendor_deliver;

  # shared_proxy_gannett_vcl_pass_gcias - req.http.x-origin-expected-host ~ "gcias-compute.usatoday.com" - return(deliver)
  call shared_proxy_gannett_vcl_deliver_gcias;

  if (req.http.host ~ "^staging-|^ci-|^origin-staging-|^origin-staging-1-|^origin-staging-2-|^cam-") {
    set resp.http.X-Robots-Tag = "noindex, nofollow";
  }

  # On non-shield nodes only for HTML pages
  if (
    fastly.ff.visits_this_service == 0 &&
    std.prefixof(resp.http.Content-Type, "text/html") &&
    req.url.path !~ "^/tangfrag/"
  ) {
    # ensure GUP cookies are set on all successful responses and 404/500 error responses for tangent
    if (
      resp.status == 200 ||
      (
        (resp.status == 404 || resp.status == 500) && req.http.x-origin-expected-host ~ "^tangent\."
      )
    ) {
      call guplib_deliver_set_missing_id_cookies;
    }
  }

  # vary on ua
  if (
    req.http.x-origin-expected-host != "usatoday.go-vip.net" &&
    ((
      std.prefixof(resp.http.Content-Type, "text/html")
    ) ||
    req.http.Gannett-Custom:vary_on_ua ||
    (
      resp.status > 300 && resp.status < 304
    ))
  ) {
    set resp.http.Vary:User-Agent = "";
  }

  # vary on Sec-CH-UA-Mobile
  if (
    req.http.x-origin-expected-host ~ "^tangent" &&
    resp.http.Vary ~ "(?i)gnt-mobile"
  ) {
    set resp.http.Vary:Sec-CH-UA-Mobile = "";
  }

  #ORD-5760 error page restarts
  if (fastly.ff.visits_this_service == 0) {
    if (req.http.x-origin-expected-host ~ "^tangent\.") {
      if (req.url.path == "/errors/404/") {
        set resp.status = 404;
      } elseif (req.url.path == "/errors/500/") {
        set resp.status = 500;
      } else {
        call shared_tangent_error_page_restart_deliver_check;
      }
    }
  }

  set req.http.Gannett-Custom:req-header-count = std.count(req.headers) "." req.restarts;

  if (!req.http.Gannett-FF || req.http.Gannett-FF == server.identity) {
    # set header to be used to prevent cookie logic from running on subresources
    call shared_helpers_tangent_can_run_cookie_logic_deliver;

    if (req.http.Gannett-Custom:can_run_cookie_logic) {

      # cookie to req object so it can be added to server_timing header later
      call shared_helpers_frontend_gnt_bot_cookie;

      # gnt_i
      call shared_helpers_frontend_set_cookie_gnt_i;

      # gnt_ti - taboola userID
      call shared_tangent_set_cookie_gnt_ti;

      # _ga - GA clientID
      call shared_tangent_set_cookie__ga;
    }
  }

  ########## UNSET HEADERS EARLY

  if (fastly.ff.visits_this_service == 0) {
    # Hide the existence of the header from downstream
    if (resp.http.Vary) {
      set resp.http.Vary = regsub(resp.http.Vary, "x-origin-expected-host,?\s?", "");
      set resp.http.Vary = regsub(resp.http.Vary, "X-AbVariant,?\s?", "");
      set resp.http.Vary = regsub(resp.http.Vary, "X-AbVCfg,?\s?", "");
      if (req.http.x-origin-expected-host ~ "^tangent") {
        set resp.http.Vary = regsub(resp.http.Vary, "Gnt-Mobile,?\s?", "");
        set resp.http.Vary = regsub(resp.http.Vary, "gnt-gm-im,?\s?", "");
        set resp.http.Vary = regsub(resp.http.Vary, "gnt-ba,?\s?", "");
      }
      set resp.http.Vary = regsub(resp.http.Vary, ",\s?$", "");
      if (resp.http.Vary == "") {
        unset resp.http.Vary;
      }
    }
    # used for logging purposes. unset them so clients do see them
    set req.http.backend_ip = resp.http.backend_ip;
    unset resp.http.backend_ip;
    unset resp.http.vcl_data;
    unset resp.http.stale_from_error;
  }

  if (resp.status >= 500 && resp.status < 600) {
    /* restart if the stale object is available and it wouldn't be the 4th restart */
    if (stale.exists) {
      if (req.restarts < 3) {
        set req.http.Gannett-Custom:error_from_shield = req.backend.is_shield;
        restart;
      } else {
        set req.http.paas_log_event = "Stale Object: Restart prevented";
        unset req.http.paas_log_event;
      }
    }
  }

  # Set Network Error Logging (NEL) and Reporting API headers
  # Add JS call stacks in crash reports policy if not already present
  call shared_helpers_general_deliver_set_reporting_api_headers;

  if (req.http.Fastly-SSL) {
    # keep Tangent or elsewhere provided header value
    if (req.http.x-origin-expected-host !~ "^tangent" || !resp.http.Content-Security-Policy) {
      # and keep from error 803 synthetic response
      if (!req.http.gnt-client:unsupported-browser && resp.status != 403) {
        # header offers an indicator to browsers to automatically issue HTTPS requests where HTTP links have been used on the page
        set resp.http.Content-Security-Policy = "upgrade-insecure-requests";
      }
    }
    if (req.http.host ~ "^(origin-staging-|origin-staging-1-|origin-staging-2-|staging-|ci-|cam-)?www") {
      set resp.http.Strict-Transport-Security = "max-age=63072000;includeSubDomains;preload";
    }
  }

#FASTLY deliver
  if (fastly.ff.visits_this_service == 0) {

    ########## UNSET HEADERS LATE
    unset resp.http.vary:Gannett-Geo-Experience;
    unset resp.http.vary:x-origin-expected-host;
    # Remove unwanted response headers from origin / Fastly
    if (req.http.Gannett-Debug) {
      set resp.http.Server-Timing = if (resp.http.Server-Timing, resp.http.Server-Timing ",", "") fastly_info.state {", fastly;desc="Edge time";dur="} time.elapsed.msec;
    } else {
      # Gannett Custom VCL
      unset resp.http.Gannett-Debug-Path-Full;
      unset resp.http.Gannett-Debug-Path;
      unset resp.http.Gannett-Debug-Version;
      # Fastly
      unset resp.http.Expires;
      unset resp.http.Pragma;
      unset resp.http.X-Cache-Hits;
      unset resp.http.X-Served-By;
      unset resp.http.X-Timer;
      unset resp.http.Via;
      # Origin
      unset resp.http.Server;
      unset resp.http.X-Powered-By;
      unset resp.http.X-Generator;
      if(req.http.x-origin-expected-host == "usatoday.go-vip.net") {
        unset resp.http.X-Rq;
        unset resp.http.X-Powered-By;
        unset resp.http.X-hacker;
        unset resp.http.Server;
        unset resp.http.Host-Header;
      }
      call shared_tangent_unset_origin_resp_headers;
    }

    # remove COOP resp header for Safari based browsers to prevent blank 304 responses
    if (
      (
        req.http.x-origin-expected-host ~ "^tangent" ||
        req.http.x-origin-expected-host == "cet-front-end-static.storage.googleapis.com"
      ) &&
      (
        req.http.Gannett-Browser:Name == "safari" ||
        req.http.Gannett-OS:Name == "iphone" ||
        req.http.Gannett-OS:Name == "ipad"
      )
    ) {
      unset resp.http.Cross-Origin-Opener-Policy;
    }

    # ensure page level responses and redirects are not stored at shared proxy caches and
    # client checks with server that cached copy is still valid
    call shared_helpers_frontend_cache_control_overrides;

    #rec-ai set cache-control
    call shared_critical_vcl_deliver_rec_ai;

    # shared_proxy_gannett_vcl_deliver_gcdn - req.http.Gannett-Custom:gcdn - unset cookie
    call shared_proxy_gannett_vcl_deliver_gcdn;

    # copy to server_timing header as backup for when cookies are disabled after cache-control has been set
    call shared_helpers_frontend_copy_gnt_i_server_timing;
    call shared_helpers_frontend_copy_gnt_bot_server_timing;

    # shared_helpers_frontend - sets client.socket.cwnd size and congestion_algorithm = bbr on the client request
    call shared_helpers_frontend_set_congestion_window;
  }
  if (req.http.Gannett-Debug) {
    if (resp.http.Gannett-Debug-Path-Full ~ "(.*) >>>> (.*)") {
      set resp.http.Gannett-Debug-Path-Full = " restarts: " req.restarts " shield: " if(req.backend.is_shield, "true", "false") " server: " server.identity " state: " fastly_info.state " path: " resp.http.Gannett-Debug-Path " >>>> " re.group.2;
    } else {
      set resp.http.Gannett-Debug-Path-Full = " restarts: " req.restarts " shield: " if(req.backend.is_shield, "true", "false") " server: " server.identity " state: " fastly_info.state " path: " resp.http.Gannett-Debug-Path;
    }
  }
  return(deliver);
}

sub vcl_error {
  # gannett boilerplate
  /* handle 971s */
  if (obj.status == 971) {
    set obj.http.Content-Type = "text/html; charset=utf-8";
    set obj.http.WWW-Authenticate = "Basic realm=Secured";
    set obj.status = 401;
    synthetic {"<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/1999/REC-html401-19991224/loose.dtd">
    <HTML>
    <HEAD>
    <TITLE>Error</TITLE>
    <META HTTP-EQUIV='Content-Type' CONTENT='text/html;'>
    </HEAD>
    <BODY><H1>401 Unauthorized</H1></BODY>
    </HTML>
    "};
    return (deliver);
  }

  # process rules to serve synthetic reponses
  call shared_helpers_front_end_process_synthetic_rules_error;

  if (obj.status == 629) {
    set obj.status = 429;
    set obj.response = "Rate Limit Exceeded";
    synthetic "Too many requests";
    return(deliver);
  }

  /* handle 701s */
  if (obj.status == 701) {
    set obj.http.Location = req.http.x-Redir-Url;
    set obj.status = 301;
    return(deliver);
  }

  /* handle 702s */
  if (obj.status == 702) {
    set obj.http.Location = req.http.x-Redir-Url;
    set obj.status = 302;
    return(deliver);
  }

  if (obj.status == 800) {
    set obj.status = 400;
    set req.http.synthetic = "true";
    synthetic {"<!DOCTYPE html><html>Bad Request</html>"};
    return(deliver);
  }

  if (obj.status == 803 && req.http.gnt-client:unsupported-browser) {
    if (req.url.path ~ "^/tangfrag/") {
      call shared_tangent_supported_browser_synthethic_tangfrag;
    } else {
      call shared_helpers_frontend_supported_browser_synthethic_gannett_default;
    }
  }

  if (obj.status == 804) {
    set obj.status = 404;
    set req.http.synthetic = "true";
    synthetic {"<!DOCTYPE html><html>Not Found</html>"};
    return(deliver);
  }

  /* handle 503s */
  if (obj.status >= 500 && obj.status < 600) {
    /* deliver stale object if it is available */
    if (stale.exists) {
      return(deliver_stale);
    }
    /* otherwise, return a synthetic */
    /* include your HTML response here */
    synthetic {"<!DOCTYPE html><html>The Site is down for maintenance.</html>"};
    return(deliver);
  }

#FASTLY error
}

sub vcl_log {
#FASTLY log
  #Googlebot traffic ingest budget collector PENG - 13215
  if (!req.http.User-Agent ~ "(?i)googlebot" && !req.http.User-Agent ~ "(?i)adsbot-google") {
    if (resp.status >= 400 && resp.status < 600) {
      # all errors get logged
      call shared_helpers_general_log_bigquery;
    } else if ( req.http.Gannett-Debug == "gnt-log" || randombool(std.atoi(table.lookup(logging_rate, "logging_percent_of_success")),100) ) { # log % of successes
      call shared_helpers_general_log_bigquery;
    }
  }
}
